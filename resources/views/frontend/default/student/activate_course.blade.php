@extends('layouts.default')
@push('title', '<PERSON><PERSON><PERSON> ho<PERSON> kh<PERSON>a học')
@push('meta')@endpush
@push('css')
<style>
    .activation-section {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        padding: 60px 0;
        position: relative;
        overflow: hidden;
    }

    .activation-section::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/><circle cx="10" cy="60" r="0.5" fill="white" opacity="0.1"/><circle cx="90" cy="40" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
        pointer-events: none;
    }

    .activation-container {
        position: relative;
        z-index: 2;
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 20px;
    }

    .activation-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border-radius: 24px;
        box-shadow:
            0 25px 50px rgba(0, 0, 0, 0.15),
            0 0 0 1px rgba(255, 255, 255, 0.2);
        padding: 60px 50px;
        max-width: 600px;
        margin: 0 auto;
        text-align: center;
        position: relative;
        overflow: hidden;
    }

    .activation-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, #ff6b6b, #feca57, #48dbfb, #ff9ff3);
        border-radius: 24px 24px 0 0;
    }

    .activation-icon {
        width: 80px;
        height: 80px;
        background: linear-gradient(135deg, #667eea, #764ba2);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 30px;
        box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
    }

    .activation-icon svg {
        width: 40px;
        height: 40px;
        fill: white;
    }

    .activation-title {
        font-size: 32px;
        font-weight: 700;
        background: linear-gradient(135deg, #667eea, #764ba2);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        margin-bottom: 15px;
        line-height: 1.2;
    }

    .activation-subtitle {
        color: #64748b;
        font-size: 16px;
        line-height: 1.6;
        margin-bottom: 40px;
        max-width: 400px;
        margin-left: auto;
        margin-right: auto;
    }

    .activation-form {
        position: relative;
    }

    .activation-input-group {
        position: relative;
        margin-bottom: 30px;
    }

    .activation-input {
        width: 100%;
        padding: 20px 24px;
        border: 2px solid #e2e8f0;
        border-radius: 16px;
        font-size: 18px;
        font-weight: 600;
        text-align: center;
        letter-spacing: 3px;
        background: #f8fafc;
        color: #334155;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        text-transform: uppercase;
    }

    .activation-input::placeholder {
        color: #94a3b8;
        font-weight: 500;
        letter-spacing: 1px;
        text-transform: none;
    }

    .activation-input:focus {
        outline: none;
        border-color: #667eea;
        background: #ffffff;
        box-shadow:
            0 0 0 4px rgba(102, 126, 234, 0.1),
            0 10px 25px rgba(102, 126, 234, 0.15);
        transform: translateY(-2px);
    }

    .activation-input:valid {
        border-color: #10b981;
        background: #f0fdf4;
    }

    .activation-btn {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        border-radius: 16px;
        padding: 18px 48px;
        font-size: 18px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        box-shadow:
            0 10px 25px rgba(102, 126, 234, 0.3),
            0 0 0 1px rgba(255, 255, 255, 0.1) inset;
        position: relative;
        overflow: hidden;
        min-width: 200px;
    }

    .activation-btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.5s;
    }

    .activation-btn:hover {
        transform: translateY(-3px);
        box-shadow:
            0 15px 35px rgba(102, 126, 234, 0.4),
            0 0 0 1px rgba(255, 255, 255, 0.2) inset;
    }

    .activation-btn:hover::before {
        left: 100%;
    }

    .activation-btn:active {
        transform: translateY(-1px);
    }

    .activation-features {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 30px;
        margin-top: 50px;
        padding-top: 40px;
        border-top: 1px solid #e2e8f0;
    }

    .activation-feature {
        text-align: center;
        padding: 20px;
    }

    .activation-feature-icon {
        width: 50px;
        height: 50px;
        background: linear-gradient(135deg, #667eea, #764ba2);
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 15px;
        opacity: 0.8;
    }

    .activation-feature-icon svg {
        width: 24px;
        height: 24px;
        fill: white;
    }

    .activation-feature-title {
        font-size: 16px;
        font-weight: 600;
        color: #334155;
        margin-bottom: 8px;
    }

    .activation-feature-desc {
        font-size: 14px;
        color: #64748b;
        line-height: 1.5;
    }

    @media (max-width: 768px) {
        .activation-section {
            padding: 40px 0;
        }

        .activation-card {
            margin: 20px;
            padding: 40px 30px;
        }

        .activation-title {
            font-size: 28px;
        }

        .activation-input {
            font-size: 16px;
            padding: 18px 20px;
        }

        .activation-btn {
            padding: 16px 40px;
            font-size: 16px;
        }

        .activation-features {
            grid-template-columns: 1fr;
            gap: 20px;
        }
    }
</style>
@endpush
@section('content')
<section class="course-content">
    <div class="container">
        <div class="row">
            @include('frontend.default.student.left_sidebar')
            <div class="col-lg-9">
                <div class="activation-form-box mt-5">
                    <h2>Nhập mã kích hoạt</h2>
                    <p>Nhập mã kích hoạt của bạn vào ô bên dưới để bắt đầu khóa học.</p>
                    <form action="{{ route('student.activate_course') }}" method="POST">
                        @csrf
                        <input type="text" name="activation_code" maxlength="20" placeholder="Nhập mã kích hoạt" required autofocus>
                        <button type="submit">Kích hoạt</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</section>
@endsection 